{"scripts": {"dev": "vite --host", "build": "tsc && vite build", "preview": "vite preview", "serve": "vite preview --host", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "lint": "eslint . --ext .ts,.tsx,.js,.jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "lint:staged": "lint-staged", "format": "prettier --write .", "format:check": "prettier --check .", "analyze": "cross-env ANALYZE=true vite build", "bundle-size": "npm run build && bundlesize", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:run": "vitest run", "clean": "rimraf dist .tsbuildinfo", "clean:deps": "rimraf node_modules package-lock.json && npm install", "prepare": "husky install", "pre-commit": "lint-staged", "pre-push": "npm run type-check && npm run test:run", "deploy": "npm run build && gh-pages -d dist", "deploy:vercel": "vercel --prod", "deploy:netlify": "netlify deploy --prod --dir=dist"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"], "*.{css,scss,less}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check && npm run test:run", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "bundlesize": [{"path": "./dist/assets/js/*.js", "maxSize": "250kb", "compression": "gzip"}, {"path": "./dist/assets/css/*.css", "maxSize": "50kb", "compression": "gzip"}], "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}