/* Modern Design System for Portfolio */

:root {
  /* Modern Color Palette - 2024 Trends */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;  /* Main brand color */
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  /* Neutral Colors - Modern Grays */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;

  /* Accent Colors */
  --accent-purple: #8b5cf6;
  --accent-emerald: #10b981;
  --accent-amber: #f59e0b;
  --accent-rose: #f43f5e;

  /* Modern Typography Scale */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-display: 'Cal Sans', 'Inter', sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;

  /* Type Scale - Perfect Fourth (1.333) */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */
  --text-5xl: 3rem;       /* 48px */
  --text-6xl: 3.75rem;    /* 60px */

  /* Modern Spacing Scale */
  --space-1: 0.25rem;     /* 4px */
  --space-2: 0.5rem;      /* 8px */
  --space-3: 0.75rem;     /* 12px */
  --space-4: 1rem;        /* 16px */
  --space-5: 1.25rem;     /* 20px */
  --space-6: 1.5rem;      /* 24px */
  --space-8: 2rem;        /* 32px */
  --space-10: 2.5rem;     /* 40px */
  --space-12: 3rem;       /* 48px */
  --space-16: 4rem;       /* 64px */
  --space-20: 5rem;       /* 80px */
  --space-24: 6rem;       /* 96px */

  /* Modern Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Modern Border Radius */
  --radius-sm: 0.375rem;   /* 6px */
  --radius-base: 0.5rem;   /* 8px */
  --radius-md: 0.75rem;    /* 12px */
  --radius-lg: 1rem;       /* 16px */
  --radius-xl: 1.5rem;     /* 24px */
  --radius-full: 9999px;

  /* Animation & Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Modern Easing Functions */
  --ease-in-out-back: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-out-expo: cubic-bezier(0.19, 1, 0.22, 1);
  --ease-in-out-circ: cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

/* Modern Typography Classes */
.text-display-large {
  font-family: var(--font-family-display);
  font-size: var(--text-6xl);
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -0.025em;
}

.text-display-medium {
  font-family: var(--font-family-display);
  font-size: var(--text-5xl);
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.text-heading-1 {
  font-family: var(--font-family-display);
  font-size: var(--text-4xl);
  font-weight: 600;
  line-height: 1.25;
  letter-spacing: -0.015em;
}

.text-heading-2 {
  font-family: var(--font-family-display);
  font-size: var(--text-3xl);
  font-weight: 600;
  line-height: 1.3;
}

.text-body-large {
  font-family: var(--font-family-sans);
  font-size: var(--text-lg);
  line-height: 1.6;
  font-weight: 400;
}

.text-body-base {
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  line-height: 1.6;
  font-weight: 400;
}

.text-caption {
  font-family: var(--font-family-sans);
  font-size: var(--text-sm);
  line-height: 1.5;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Modern Component Styles */
.modern-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  transition: all var(--transition-base);
}

.modern-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.modern-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-family: var(--font-family-sans);
  font-size: var(--text-sm);
  font-weight: 500;
  border-radius: var(--radius-base);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
}

.modern-button--primary {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  box-shadow: var(--shadow-sm);
}

.modern-button--primary:hover {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.modern-button--ghost {
  background: transparent;
  color: var(--neutral-300);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-button--ghost:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

/* Modern Gradient Backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
}

.gradient-accent {
  background: linear-gradient(135deg, var(--accent-purple), var(--primary-600));
}

.gradient-mesh {
  background: 
    radial-gradient(at 40% 20%, var(--primary-600) 0px, transparent 50%),
    radial-gradient(at 80% 0%, var(--accent-purple) 0px, transparent 50%),
    radial-gradient(at 0% 50%, var(--primary-800) 0px, transparent 50%),
    radial-gradient(at 80% 50%, var(--accent-emerald) 0px, transparent 50%),
    radial-gradient(at 0% 100%, var(--primary-700) 0px, transparent 50%),
    radial-gradient(at 80% 100%, var(--accent-amber) 0px, transparent 50%),
    radial-gradient(at 0% 0%, var(--accent-rose) 0px, transparent 50%);
}

/* Modern Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s var(--ease-out-expo) forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s var(--ease-out-expo) forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s var(--ease-out-expo) forwards;
}

/* Modern Utility Classes */
.glass-effect {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-400), var(--accent-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hover-lift {
  transition: transform var(--transition-base);
}

.hover-lift:hover {
  transform: translateY(-4px);
}
