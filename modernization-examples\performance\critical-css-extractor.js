// Critical CSS Extraction and Optimization

const critical = require('critical');
const fs = require('fs').promises;
const path = require('path');
const postcss = require('postcss');
const cssnano = require('cssnano');
const autoprefixer = require('autoprefixer');

class CriticalCSSExtractor {
  constructor(options = {}) {
    this.options = {
      base: 'dist/',
      inline: true,
      minify: true,
      extract: true,
      dimensions: [
        { width: 320, height: 568 },   // Mobile
        { width: 768, height: 1024 },  // Tablet
        { width: 1200, height: 900 },  // Desktop
        { width: 1920, height: 1080 }, // Large Desktop
      ],
      penthouse: {
        timeout: 30000,
        pageLoadSkipTimeout: 5000,
        maxEmbeddedBase64Length: 1000,
        userAgent: 'Mozilla/5.0 (compatible; CriticalCSS)',
      },
      ...options,
    };
  }

  async extractCritical(htmlFile, cssFiles) {
    try {
      console.log(`Extracting critical CSS for ${htmlFile}...`);
      
      const result = await critical.generate({
        ...this.options,
        src: htmlFile,
        css: cssFiles,
        target: {
          css: path.join(this.options.base, 'critical.css'),
          html: htmlFile,
          uncritical: path.join(this.options.base, 'non-critical.css'),
        },
      });

      console.log(`Critical CSS extracted successfully for ${htmlFile}`);
      return result;
    } catch (error) {
      console.error(`Error extracting critical CSS for ${htmlFile}:`, error);
      throw error;
    }
  }

  async optimizeCSS(cssContent) {
    const result = await postcss([
      autoprefixer(),
      cssnano({
        preset: ['default', {
          discardComments: { removeAll: true },
          normalizeWhitespace: true,
          colormin: true,
          convertValues: true,
          discardDuplicates: true,
          discardEmpty: true,
          discardOverridden: true,
          discardUnused: true,
          mergeIdents: true,
          mergeLonghand: true,
          mergeRules: true,
          minifyFontValues: true,
          minifyGradients: true,
          minifyParams: true,
          minifySelectors: true,
          normalizeCharset: true,
          normalizeDisplayValues: true,
          normalizePositions: true,
          normalizeRepeatStyle: true,
          normalizeString: true,
          normalizeTimingFunctions: true,
          normalizeUnicode: true,
          normalizeUrl: true,
          orderedValues: true,
          reduceIdents: true,
          reduceInitial: true,
          reduceTransforms: true,
          svgo: true,
          uniqueSelectors: true,
        }],
      }),
    ]).process(cssContent, { from: undefined });

    return result.css;
  }

  async generateInlineCSS(criticalCSS) {
    const optimized = await this.optimizeCSS(criticalCSS);
    return `<style>${optimized}</style>`;
  }

  async processMultiplePages(pages) {
    const results = [];
    
    for (const page of pages) {
      try {
        const result = await this.extractCritical(page.html, page.css);
        results.push({
          page: page.name,
          success: true,
          result,
        });
      } catch (error) {
        results.push({
          page: page.name,
          success: false,
          error: error.message,
        });
      }
    }

    return results;
  }

  async generateResourceHints(assets) {
    const hints = [];

    // DNS prefetch for external domains
    const externalDomains = new Set();
    assets.forEach(asset => {
      try {
        const url = new URL(asset);
        if (url.hostname !== window.location.hostname) {
          externalDomains.add(url.hostname);
        }
      } catch (e) {
        // Not a valid URL, skip
      }
    });

    externalDomains.forEach(domain => {
      hints.push(`<link rel="dns-prefetch" href="//${domain}">`);
    });

    // Preload critical assets
    const criticalAssets = assets.filter(asset => 
      asset.includes('critical') || 
      asset.includes('above-fold') ||
      asset.includes('hero')
    );

    criticalAssets.forEach(asset => {
      const ext = path.extname(asset).toLowerCase();
      let asType = 'fetch';
      
      if (['.css'].includes(ext)) asType = 'style';
      else if (['.js'].includes(ext)) asType = 'script';
      else if (['.woff', '.woff2'].includes(ext)) asType = 'font';
      else if (['.jpg', '.jpeg', '.png', '.webp', '.avif'].includes(ext)) asType = 'image';

      hints.push(`<link rel="preload" href="${asset}" as="${asType}"${asType === 'font' ? ' crossorigin' : ''}>`);
    });

    return hints.join('\n');
  }
}

// Usage example and configuration
const portfolioOptimizer = {
  async optimizePortfolio() {
    const extractor = new CriticalCSSExtractor({
      base: 'dist/',
      inline: true,
      minify: true,
      extract: true,
      ignore: [
        '@font-face',
        /url\(/,
        /print/,
        /.sr-only/,
        /.hidden/,
      ],
    });

    const pages = [
      {
        name: 'home',
        html: 'dist/index.html',
        css: ['dist/assets/css/main.css'],
      },
    ];

    try {
      const results = await extractor.processMultiplePages(pages);
      console.log('Critical CSS extraction completed:', results);
      
      // Generate resource hints
      const assets = [
        '/assets/css/main.css',
        '/assets/js/main.js',
        '/assets/fonts/inter.woff2',
        'https://fonts.googleapis.com',
      ];
      
      const resourceHints = await extractor.generateResourceHints(assets);
      console.log('Resource hints generated:', resourceHints);
      
      return { results, resourceHints };
    } catch (error) {
      console.error('Portfolio optimization failed:', error);
      throw error;
    }
  },

  // Inline critical CSS in HTML
  async inlineCriticalCSS(htmlContent, criticalCSS) {
    const optimizedCSS = await new CriticalCSSExtractor().optimizeCSS(criticalCSS);
    
    // Insert critical CSS before closing head tag
    const criticalStyle = `<style>${optimizedCSS}</style>`;
    const updatedHTML = htmlContent.replace('</head>', `${criticalStyle}\n</head>`);
    
    // Add preload for non-critical CSS
    const preloadCSS = '<link rel="preload" href="/assets/css/non-critical.css" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">';
    
    return updatedHTML.replace('</head>', `${preloadCSS}\n</head>`);
  },

  // Generate service worker for caching
  generateServiceWorker() {
    return `
// Service Worker for Portfolio Caching
const CACHE_NAME = 'portfolio-v1';
const CRITICAL_ASSETS = [
  '/',
  '/assets/css/critical.css',
  '/assets/js/critical.js',
  '/assets/fonts/inter.woff2',
];

const NON_CRITICAL_ASSETS = [
  '/assets/css/non-critical.css',
  '/assets/js/non-critical.js',
  '/assets/images/',
];

// Install event - cache critical assets
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(CRITICAL_ASSETS))
      .then(() => self.skipWaiting())
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames
            .filter((cacheName) => cacheName !== CACHE_NAME)
            .map((cacheName) => caches.delete(cacheName))
        );
      })
      .then(() => self.clients.claim())
  );
});

// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request)
          .then((fetchResponse) => {
            // Cache non-critical assets on demand
            if (shouldCache(event.request.url)) {
              const responseClone = fetchResponse.clone();
              caches.open(CACHE_NAME)
                .then((cache) => cache.put(event.request, responseClone));
            }
            return fetchResponse;
          });
      })
      .catch(() => {
        // Fallback for offline
        if (event.request.destination === 'document') {
          return caches.match('/offline.html');
        }
      })
  );
});

function shouldCache(url) {
  return NON_CRITICAL_ASSETS.some(asset => url.includes(asset));
}
`;
  },
};

module.exports = { CriticalCSSExtractor, portfolioOptimizer };
