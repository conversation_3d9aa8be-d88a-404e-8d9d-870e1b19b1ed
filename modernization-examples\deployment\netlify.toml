[build]
  publish = "dist"
  command = "npm run build"
  
[build.environment]
  NODE_ENV = "production"
  NODE_VERSION = "18"
  NPM_FLAGS = "--prefix=/dev/null"

# Build plugins
[[plugins]]
  package = "@netlify/plugin-nextjs"

[[plugins]]
  package = "netlify-plugin-submit-sitemap"
  [plugins.inputs]
    baseUrl = "https://melvinsalonga.netlify.app"
    sitemapPath = "/sitemap.xml"
    ignorePeriod = 0
    providers = [
      "google",
      "bing"
    ]

[[plugins]]
  package = "netlify-plugin-lighthouse"
  [plugins.inputs]
    output_path = "reports/lighthouse.html"

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=(), interest-cohort=()"
    Strict-Transport-Security = "max-age=31536000; includeSubDomains; preload"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
    Access-Control-Allow-Origin = "*"

[[headers]]
  for = "/sw.js"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Redirects
[[redirects]]
  from = "/resume"
  to = "/assets/file/Melvin-Salonga-Applicant-Resume.pdf"
  status = 302

[[redirects]]
  from = "/cv"
  to = "/assets/file/Melvin-Salonga-Applicant-Resume.pdf"
  status = 302

[[redirects]]
  from = "/github"
  to = "https://github.com/melvsalonga"
  status = 302

[[redirects]]
  from = "/linkedin"
  to = "https://www.linkedin.com/in/john-melvin-salonga/"
  status = 302

# SPA fallback
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Form handling
[forms]
  [forms.contact]
    name = "contact"
    action = "/thank-you"
    method = "POST"

# Edge functions
[[edge_functions]]
  function = "analytics"
  path = "/*"

# Environment variables
[context.production.environment]
  NEXT_PUBLIC_SITE_URL = "https://melvinsalonga.netlify.app"
  NEXT_PUBLIC_GA_ID = "G-XXXXXXXXXX"

[context.deploy-preview.environment]
  NEXT_PUBLIC_SITE_URL = "https://deploy-preview-$REVIEW_ID--melvinsalonga.netlify.app"

[context.branch-deploy.environment]
  NEXT_PUBLIC_SITE_URL = "https://$BRANCH--melvinsalonga.netlify.app"

# Split testing
[split_testing]
  [split_testing.hero_version]
    path = "/"
    branches = [
      { branch = "main", percentage = 80 },
      { branch = "hero-v2", percentage = 20 }
    ]
