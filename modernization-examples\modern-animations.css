/* Modern Animations & Effects */

/* Custom Cursor Styles */
.custom-cursor {
  position: fixed;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
}

.cursor-dot {
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: -4px;
  left: -4px;
}

.cursor-outline {
  width: 32px;
  height: 32px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  position: absolute;
  top: -16px;
  left: -16px;
  transition: all 0.15s ease-out;
}

.custom-cursor.cursor-hover .cursor-outline {
  width: 48px;
  height: 48px;
  top: -24px;
  left: -24px;
  border-color: var(--primary-400);
}

/* Scroll Animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-on-scroll.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.stagger-item {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.stagger-item.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Tilt Card Effect */
.tilt-card {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-style: preserve-3d;
}

.tilt-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tilt-card:hover::before {
  opacity: 1;
}

/* Ripple Effect */
.ripple-button {
  position: relative;
  overflow: hidden;
}

.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Magnetic Button Effect */
.magnetic-button {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Text Reveal Animations */
.char-reveal {
  display: inline-block;
  opacity: 0;
  transform: translateY(100%);
  animation: char-reveal-animation 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes char-reveal-animation {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.word-reveal-item {
  display: inline-block;
  opacity: 0;
  transform: translateY(50px);
  animation: word-reveal-animation 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes word-reveal-animation {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modern Loader */
.modern-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--neutral-900);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  transition: opacity 0.5s ease;
}

.modern-loader.loader-complete {
  opacity: 0;
}

.loader-content {
  text-align: center;
}

.loader-text {
  font-family: var(--font-family-display);
  font-size: var(--text-2xl);
  font-weight: 700;
  color: white;
  margin-bottom: var(--space-8);
}

.loader-letter {
  display: inline-block;
  animation: loader-bounce 1.4s infinite ease-in-out both;
}

.loader-letter:nth-child(1) { animation-delay: -0.32s; }
.loader-letter:nth-child(2) { animation-delay: -0.16s; }
.loader-letter:nth-child(3) { animation-delay: 0s; }
.loader-letter:nth-child(4) { animation-delay: 0.16s; }
.loader-letter:nth-child(5) { animation-delay: 0.32s; }
.loader-letter:nth-child(6) { animation-delay: 0.48s; }
.loader-letter:nth-child(7) { animation-delay: 0.64s; }

@keyframes loader-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loader-progress {
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin: 0 auto;
}

.loader-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-500), var(--accent-purple));
  border-radius: 2px;
  transition: width 0.3s ease;
  width: 0%;
}

/* Floating Elements */
.floating-element {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Pulse Animation */
.pulse-element {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Glow Effect */
.glow-effect {
  position: relative;
}

.glow-effect::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--primary-500), var(--accent-purple), var(--accent-emerald), var(--primary-500));
  border-radius: inherit;
  z-index: -1;
  filter: blur(10px);
  opacity: 0;
  transition: opacity 0.3s ease;
  animation: glow-rotate 4s linear infinite;
}

.glow-effect:hover::before {
  opacity: 0.7;
}

@keyframes glow-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Morphing Shapes */
.morphing-blob {
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  animation: morph 8s ease-in-out infinite;
}

@keyframes morph {
  0%, 100% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  }
  25% {
    border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
  }
  50% {
    border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
  }
  75% {
    border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
  }
}

/* Slide In Animations */
.slide-in-left {
  opacity: 0;
  transform: translateX(-100px);
  animation: slideInLeft 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.slide-in-right {
  opacity: 0;
  transform: translateX(100px);
  animation: slideInRight 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.slide-in-up {
  opacity: 0;
  transform: translateY(100px);
  animation: slideInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.slide-in-down {
  opacity: 0;
  transform: translateY(-100px);
  animation: slideInDown 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scale Animations */
.scale-in {
  opacity: 0;
  transform: scale(0.8);
  animation: scaleIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes scaleIn {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Rotate Animations */
.rotate-in {
  opacity: 0;
  transform: rotate(-180deg) scale(0.8);
  animation: rotateIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes rotateIn {
  to {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

/* Utility Classes for Animation Delays */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }
.delay-700 { animation-delay: 0.7s; }
.delay-1000 { animation-delay: 1s; }
