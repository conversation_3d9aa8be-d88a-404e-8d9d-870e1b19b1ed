{"ci": {"collect": {"url": ["http://localhost:3000", "http://localhost:3000/about", "http://localhost:3000/projects", "http://localhost:3000/contact"], "startServerCommand": "npm run serve", "startServerReadyPattern": "Local:", "numberOfRuns": 3, "settings": {"preset": "desktop", "chromeFlags": "--no-sandbox --headless", "throttling": {"rttMs": 40, "throughputKbps": 10240, "cpuSlowdownMultiplier": 1, "requestLatencyMs": 0, "downloadThroughputKbps": 0, "uploadThroughputKbps": 0}, "emulatedFormFactor": "desktop", "locale": "en-US"}}, "assert": {"assertions": {"categories:performance": ["error", {"minScore": 0.9}], "categories:accessibility": ["error", {"minScore": 0.95}], "categories:best-practices": ["error", {"minScore": 0.9}], "categories:seo": ["error", {"minScore": 0.95}], "categories:pwa": ["warn", {"minScore": 0.8}]}}, "upload": {"target": "temporary-public-storage"}, "server": {"port": 9001, "storage": {"storageMethod": "sql", "sqlDialect": "sqlite", "sqlDatabasePath": "./lhci.db"}}, "wizard": {"preset": "nextjs"}}}