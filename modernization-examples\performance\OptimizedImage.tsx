'use client';

import { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { clsx } from 'clsx';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  sizes?: string;
  quality?: number;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
}

// Generate responsive image URLs
const generateSrcSet = (src: string, quality: number = 75) => {
  const sizes = [640, 750, 828, 1080, 1200, 1920];
  const webpSrcSet = sizes
    .map(size => `${src}?w=${size}&q=${quality}&f=webp ${size}w`)
    .join(', ');
  const fallbackSrcSet = sizes
    .map(size => `${src}?w=${size}&q=${quality} ${size}w`)
    .join(', ');
  
  return { webpSrcSet, fallbackSrcSet };
};

// Generate blur placeholder
const generateBlurDataURL = (width: number = 10, height: number = 10) => {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext('2d');
  
  if (ctx) {
    // Create a simple gradient blur effect
    const gradient = ctx.createLinearGradient(0, 0, width, height);
    gradient.addColorStop(0, '#f3f4f6');
    gradient.addColorStop(1, '#e5e7eb');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
  }
  
  return canvas.toDataURL();
};

export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  placeholder = 'blur',
  blurDataURL,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  quality = 75,
  loading = 'lazy',
  onLoad,
  onError,
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(priority);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || isInView) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px',
        threshold: 0.1,
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [priority, isInView]);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  const { webpSrcSet, fallbackSrcSet } = generateSrcSet(src, quality);
  const defaultBlurDataURL = blurDataURL || generateBlurDataURL(width, height);

  return (
    <div
      ref={containerRef}
      className={clsx('relative overflow-hidden', className)}
      style={{ width, height }}
    >
      {/* Blur Placeholder */}
      {placeholder === 'blur' && !isLoaded && (
        <motion.div
          initial={{ opacity: 1 }}
          animate={{ opacity: isLoaded ? 0 : 1 }}
          transition={{ duration: 0.3 }}
          className="absolute inset-0 bg-neutral-200"
          style={{
            backgroundImage: `url(${defaultBlurDataURL})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            filter: 'blur(10px)',
            transform: 'scale(1.1)',
          }}
        />
      )}

      {/* Loading Skeleton */}
      {!isLoaded && !hasError && (
        <div className="absolute inset-0 bg-neutral-200 animate-pulse">
          <div className="w-full h-full bg-gradient-to-r from-neutral-200 via-neutral-100 to-neutral-200 animate-shimmer" />
        </div>
      )}

      {/* Optimized Image */}
      {isInView && !hasError && (
        <picture>
          {/* WebP format for modern browsers */}
          <source
            srcSet={webpSrcSet}
            sizes={sizes}
            type="image/webp"
          />
          
          {/* Fallback format */}
          <motion.img
            ref={imgRef}
            src={`${src}?w=${width}&q=${quality}`}
            srcSet={fallbackSrcSet}
            alt={alt}
            width={width}
            height={height}
            sizes={sizes}
            loading={priority ? 'eager' : loading}
            decoding="async"
            onLoad={handleLoad}
            onError={handleError}
            initial={{ opacity: 0 }}
            animate={{ opacity: isLoaded ? 1 : 0 }}
            transition={{ duration: 0.3 }}
            className={clsx(
              'w-full h-full object-cover transition-opacity duration-300',
              isLoaded ? 'opacity-100' : 'opacity-0'
            )}
          />
        </picture>
      )}

      {/* Error State */}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-neutral-100 text-neutral-500">
          <div className="text-center">
            <svg
              className="w-12 h-12 mx-auto mb-2 text-neutral-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <p className="text-sm">Failed to load image</p>
          </div>
        </div>
      )}

      {/* Loading Progress Indicator */}
      {!isLoaded && !hasError && isInView && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-neutral-200">
          <motion.div
            className="h-full bg-primary-500"
            initial={{ width: '0%' }}
            animate={{ width: '100%' }}
            transition={{ duration: 2, ease: 'easeInOut' }}
          />
        </div>
      )}
    </div>
  );
}

// Utility hook for preloading images
export const useImagePreloader = (imageSources: string[]) => {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());

  useEffect(() => {
    const preloadImage = (src: string) => {
      return new Promise<string>((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve(src);
        img.onerror = reject;
        img.src = src;
      });
    };

    const preloadImages = async () => {
      try {
        const promises = imageSources.map(preloadImage);
        const loaded = await Promise.allSettled(promises);
        
        const successful = loaded
          .filter((result): result is PromiseFulfilledResult<string> => 
            result.status === 'fulfilled'
          )
          .map(result => result.value);

        setLoadedImages(new Set(successful));
      } catch (error) {
        console.warn('Some images failed to preload:', error);
      }
    };

    if (imageSources.length > 0) {
      preloadImages();
    }
  }, [imageSources]);

  return loadedImages;
};

// Image optimization utilities
export const imageOptimizer = {
  // Convert image to WebP format
  toWebP: (src: string, quality: number = 75) => {
    return `${src}?f=webp&q=${quality}`;
  },

  // Resize image
  resize: (src: string, width: number, height?: number, quality: number = 75) => {
    const params = new URLSearchParams({
      w: width.toString(),
      q: quality.toString(),
    });
    
    if (height) {
      params.set('h', height.toString());
    }
    
    return `${src}?${params.toString()}`;
  },

  // Generate responsive sizes
  responsive: (src: string, sizes: number[], quality: number = 75) => {
    return sizes.map(size => ({
      src: `${src}?w=${size}&q=${quality}`,
      width: size,
    }));
  },

  // Generate blur placeholder
  placeholder: (src: string, width: number = 10, height: number = 10) => {
    return `${src}?w=${width}&h=${height}&blur=10&q=1`;
  },
};
