// Modern Micro-Interactions for Portfolio

class ModernInteractions {
  constructor() {
    this.init();
  }

  init() {
    this.setupCursorEffects();
    this.setupScrollAnimations();
    this.setupHoverEffects();
    this.setupParallaxEffects();
    this.setupMagneticButtons();
    this.setupTextRevealAnimations();
  }

  // Modern Cursor Effects
  setupCursorEffects() {
    const cursor = document.createElement('div');
    cursor.className = 'custom-cursor';
    cursor.innerHTML = '<div class="cursor-dot"></div><div class="cursor-outline"></div>';
    document.body.appendChild(cursor);

    let mouseX = 0, mouseY = 0;
    let cursorX = 0, cursorY = 0;

    document.addEventListener('mousemove', (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
    });

    // Smooth cursor animation
    const animateCursor = () => {
      const speed = 0.15;
      cursorX += (mouseX - cursorX) * speed;
      cursorY += (mouseY - cursorY) * speed;
      
      cursor.style.transform = `translate(${cursorX}px, ${cursorY}px)`;
      requestAnimationFrame(animateCursor);
    };
    animateCursor();

    // Cursor interactions
    document.querySelectorAll('a, button, .interactive').forEach(el => {
      el.addEventListener('mouseenter', () => cursor.classList.add('cursor-hover'));
      el.addEventListener('mouseleave', () => cursor.classList.remove('cursor-hover'));
    });
  }

  // Advanced Scroll Animations with Intersection Observer
  setupScrollAnimations() {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
          
          // Stagger animations for child elements
          const children = entry.target.querySelectorAll('.stagger-item');
          children.forEach((child, index) => {
            setTimeout(() => {
              child.classList.add('animate-in');
            }, index * 100);
          });
        }
      });
    }, observerOptions);

    document.querySelectorAll('.animate-on-scroll').forEach(el => {
      observer.observe(el);
    });
  }

  // Modern Hover Effects
  setupHoverEffects() {
    // Tilt effect for cards
    document.querySelectorAll('.tilt-card').forEach(card => {
      card.addEventListener('mousemove', (e) => {
        const rect = card.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        
        const rotateX = (y - centerY) / 10;
        const rotateY = (centerX - x) / 10;
        
        card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.05, 1.05, 1.05)`;
      });
      
      card.addEventListener('mouseleave', () => {
        card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1)';
      });
    });

    // Ripple effect for buttons
    document.querySelectorAll('.ripple-button').forEach(button => {
      button.addEventListener('click', (e) => {
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');
        
        button.appendChild(ripple);
        
        setTimeout(() => ripple.remove(), 600);
      });
    });
  }

  // Parallax Effects
  setupParallaxEffects() {
    const parallaxElements = document.querySelectorAll('.parallax-element');
    
    window.addEventListener('scroll', () => {
      const scrolled = window.pageYOffset;
      
      parallaxElements.forEach(element => {
        const speed = element.dataset.speed || 0.5;
        const yPos = -(scrolled * speed);
        element.style.transform = `translateY(${yPos}px)`;
      });
    });
  }

  // Magnetic Buttons
  setupMagneticButtons() {
    document.querySelectorAll('.magnetic-button').forEach(button => {
      button.addEventListener('mousemove', (e) => {
        const rect = button.getBoundingClientRect();
        const x = e.clientX - rect.left - rect.width / 2;
        const y = e.clientY - rect.top - rect.height / 2;
        
        button.style.transform = `translate(${x * 0.3}px, ${y * 0.3}px)`;
      });
      
      button.addEventListener('mouseleave', () => {
        button.style.transform = 'translate(0px, 0px)';
      });
    });
  }

  // Text Reveal Animations
  setupTextRevealAnimations() {
    document.querySelectorAll('.text-reveal').forEach(element => {
      const text = element.textContent;
      element.innerHTML = '';
      
      // Split text into spans
      text.split('').forEach((char, index) => {
        const span = document.createElement('span');
        span.textContent = char === ' ' ? '\u00A0' : char;
        span.style.animationDelay = `${index * 0.05}s`;
        span.classList.add('char-reveal');
        element.appendChild(span);
      });
    });

    // Word reveal animation
    document.querySelectorAll('.word-reveal').forEach(element => {
      const words = element.textContent.split(' ');
      element.innerHTML = '';
      
      words.forEach((word, index) => {
        const span = document.createElement('span');
        span.textContent = word;
        span.style.animationDelay = `${index * 0.2}s`;
        span.classList.add('word-reveal-item');
        element.appendChild(span);
        
        if (index < words.length - 1) {
          element.appendChild(document.createTextNode(' '));
        }
      });
    });
  }

  // Smooth scroll with easing
  smoothScrollTo(target, duration = 1000) {
    const targetElement = document.querySelector(target);
    if (!targetElement) return;

    const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;

    const easeInOutCubic = (t) => {
      return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    };

    const animation = (currentTime) => {
      if (startTime === null) startTime = currentTime;
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      const ease = easeInOutCubic(progress);
      
      window.scrollTo(0, startPosition + distance * ease);
      
      if (timeElapsed < duration) {
        requestAnimationFrame(animation);
      }
    };

    requestAnimationFrame(animation);
  }

  // Loading animation
  createLoadingAnimation() {
    const loader = document.createElement('div');
    loader.className = 'modern-loader';
    loader.innerHTML = `
      <div class="loader-content">
        <div class="loader-text">
          <span class="loader-letter">L</span>
          <span class="loader-letter">O</span>
          <span class="loader-letter">A</span>
          <span class="loader-letter">D</span>
          <span class="loader-letter">I</span>
          <span class="loader-letter">N</span>
          <span class="loader-letter">G</span>
        </div>
        <div class="loader-progress">
          <div class="loader-bar"></div>
        </div>
      </div>
    `;
    
    document.body.appendChild(loader);
    
    // Simulate loading progress
    let progress = 0;
    const progressBar = loader.querySelector('.loader-bar');
    
    const updateProgress = () => {
      progress += Math.random() * 15;
      if (progress > 100) progress = 100;
      
      progressBar.style.width = `${progress}%`;
      
      if (progress < 100) {
        setTimeout(updateProgress, 100 + Math.random() * 200);
      } else {
        setTimeout(() => {
          loader.classList.add('loader-complete');
          setTimeout(() => loader.remove(), 500);
        }, 500);
      }
    };
    
    updateProgress();
  }
}

// Initialize modern interactions when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new ModernInteractions();
});

// Export for module usage
export default ModernInteractions;
