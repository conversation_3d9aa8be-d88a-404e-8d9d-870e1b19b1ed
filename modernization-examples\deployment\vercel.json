{"version": 2, "name": "melvin-portfolio", "alias": ["melvinsalonga.dev", "www.melvinsalonga.dev"], "build": {"env": {"NODE_ENV": "production", "NEXT_TELEMETRY_DISABLED": "1"}}, "buildCommand": "npm run build", "outputDirectory": "out", "installCommand": "npm ci", "functions": {"app/api/**/*.ts": {"runtime": "nodejs18.x", "maxDuration": 10}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=(), interest-cohort=()"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}], "redirects": [{"source": "/resume", "destination": "/assets/file/<PERSON>-<PERSON>-Applicant-Resume.pdf", "permanent": false}, {"source": "/cv", "destination": "/assets/file/<PERSON>-<PERSON>-Applicant-Resume.pdf", "permanent": false}, {"source": "/github", "destination": "https://github.com/melvsalonga", "permanent": false}, {"source": "/linkedin", "destination": "https://www.linkedin.com/in/john-melvin-salonga/", "permanent": false}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}, {"source": "/robots.txt", "destination": "/api/robots"}], "trailingSlash": false, "cleanUrls": true, "framework": "nextjs", "regions": ["iad1", "sfo1", "lhr1"], "env": {"NEXT_PUBLIC_SITE_URL": "https://melvinsalonga.dev", "NEXT_PUBLIC_GA_ID": "G-XXXXXXXXXX"}, "github": {"enabled": true, "autoAlias": true, "autoJobCancelation": true, "silent": true}, "images": {"domains": ["images.unsplash.com", "via.placeholder.com"], "formats": ["image/webp", "image/avif"], "minimumCacheTTL": 31536000, "dangerouslyAllowSVG": false, "contentSecurityPolicy": "default-src 'self'; script-src 'none'; sandbox;"}}