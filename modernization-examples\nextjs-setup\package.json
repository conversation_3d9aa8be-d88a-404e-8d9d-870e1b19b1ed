{"name": "melvin-portfolio-modern", "version": "1.0.0", "description": "Modern portfolio website for <PERSON>", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write .", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "framer-motion": "^10.16.16", "next-themes": "^0.2.1", "lucide-react": "^0.303.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "@next/font": "^14.0.4", "sharp": "^0.33.1"}, "devDependencies": {"@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "typescript": "^5.3.3", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "prettier": "^3.1.1", "tailwindcss": "^3.4.0", "postcss": "^8.4.32", "autoprefixer": "^10.4.16", "@tailwindcss/typography": "^0.5.10", "cross-env": "^7.0.3", "@next/bundle-analyzer": "^14.0.4"}, "engines": {"node": ">=18.0.0"}}