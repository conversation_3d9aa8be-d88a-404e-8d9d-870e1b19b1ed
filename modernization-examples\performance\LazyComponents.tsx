'use client';

import { lazy, Suspense, ComponentType, ReactNode } from 'react';
import { motion } from 'framer-motion';

// Loading component with skeleton
const LoadingSkeleton = ({ 
  height = 'h-64', 
  className = '' 
}: { 
  height?: string; 
  className?: string; 
}) => (
  <div className={`animate-pulse ${height} ${className}`}>
    <div className="bg-neutral-200 rounded-lg h-full">
      <div className="p-6 space-y-4">
        <div className="h-4 bg-neutral-300 rounded w-3/4"></div>
        <div className="h-4 bg-neutral-300 rounded w-1/2"></div>
        <div className="h-4 bg-neutral-300 rounded w-5/6"></div>
      </div>
    </div>
  </div>
);

// Enhanced loading component with animation
const AnimatedLoader = ({ message = 'Loading...' }: { message?: string }) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.9 }}
    animate={{ opacity: 1, scale: 1 }}
    exit={{ opacity: 0, scale: 0.9 }}
    className="flex flex-col items-center justify-center p-8"
  >
    <div className="relative">
      <div className="w-12 h-12 border-4 border-neutral-200 rounded-full"></div>
      <div className="absolute top-0 left-0 w-12 h-12 border-4 border-primary-500 rounded-full border-t-transparent animate-spin"></div>
    </div>
    <p className="mt-4 text-neutral-600 font-medium">{message}</p>
  </motion.div>
);

// Lazy load components with error boundaries
const createLazyComponent = <T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: ReactNode
) => {
  const LazyComponent = lazy(importFunc);
  
  return (props: React.ComponentProps<T>) => (
    <Suspense fallback={fallback || <LoadingSkeleton />}>
      <LazyComponent {...props} />
    </Suspense>
  );
};

// Lazy loaded sections
export const LazyAboutSection = createLazyComponent(
  () => import('../components/AboutSection'),
  <LoadingSkeleton height="h-96" className="mb-8" />
);

export const LazySkillsSection = createLazyComponent(
  () => import('../components/SkillsSection'),
  <LoadingSkeleton height="h-80" className="mb-8" />
);

export const LazyProjectsSection = createLazyComponent(
  () => import('../components/ProjectsSection'),
  <LoadingSkeleton height="h-screen" className="mb-8" />
);

export const LazyContactSection = createLazyComponent(
  () => import('../components/ContactSection'),
  <LoadingSkeleton height="h-96" className="mb-8" />
);

// Lazy load heavy components
export const LazyParticleBackground = createLazyComponent(
  () => import('../components/ParticleBackground'),
  <div className="absolute inset-0 bg-gradient-mesh opacity-30" />
);

export const LazyThreeJSScene = createLazyComponent(
  () => import('../components/ThreeJSScene'),
  <AnimatedLoader message="Loading 3D Scene..." />
);

export const LazyCodeEditor = createLazyComponent(
  () => import('../components/CodeEditor'),
  <AnimatedLoader message="Loading Code Editor..." />
);

// Intersection Observer based lazy loading
export const LazyOnScroll = ({ 
  children, 
  threshold = 0.1, 
  rootMargin = '50px',
  fallback = <LoadingSkeleton />
}: {
  children: ReactNode;
  threshold?: number;
  rootMargin?: string;
  fallback?: ReactNode;
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold, rootMargin }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [threshold, rootMargin]);

  return (
    <div ref={ref}>
      {isVisible ? children : fallback}
    </div>
  );
};

// Progressive enhancement for heavy features
export const ProgressiveFeature = ({ 
  children, 
  fallback,
  condition = () => true 
}: {
  children: ReactNode;
  fallback: ReactNode;
  condition?: () => boolean;
}) => {
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    // Check if the feature should be enabled
    const checkCondition = () => {
      if (condition()) {
        setShouldRender(true);
      }
    };

    // Delay the check to avoid blocking initial render
    const timer = setTimeout(checkCondition, 100);
    return () => clearTimeout(timer);
  }, [condition]);

  return shouldRender ? <>{children}</> : <>{fallback}</>;
};

// Resource preloader
export const useResourcePreloader = (resources: string[]) => {
  const [loadedResources, setLoadedResources] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const preloadResource = (url: string): Promise<string> => {
      return new Promise((resolve, reject) => {
        if (url.endsWith('.js')) {
          const script = document.createElement('script');
          script.src = url;
          script.onload = () => resolve(url);
          script.onerror = reject;
          document.head.appendChild(script);
        } else if (url.endsWith('.css')) {
          const link = document.createElement('link');
          link.rel = 'stylesheet';
          link.href = url;
          link.onload = () => resolve(url);
          link.onerror = reject;
          document.head.appendChild(link);
        } else {
          // For other resources (images, fonts, etc.)
          fetch(url)
            .then(() => resolve(url))
            .catch(reject);
        }
      });
    };

    const preloadAll = async () => {
      try {
        const promises = resources.map(preloadResource);
        const results = await Promise.allSettled(promises);
        
        const loaded = results
          .filter((result): result is PromiseFulfilledResult<string> => 
            result.status === 'fulfilled'
          )
          .map(result => result.value);

        setLoadedResources(new Set(loaded));
      } catch (error) {
        console.warn('Some resources failed to preload:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (resources.length > 0) {
      preloadAll();
    } else {
      setIsLoading(false);
    }
  }, [resources]);

  return { loadedResources, isLoading };
};

// Bundle splitting utilities
export const bundleSplitter = {
  // Split by route
  byRoute: (routeName: string) => 
    lazy(() => import(`../pages/${routeName}`)),

  // Split by feature
  byFeature: (featureName: string) => 
    lazy(() => import(`../features/${featureName}`)),

  // Split by vendor
  byVendor: (vendorName: string) => 
    lazy(() => import(`../vendors/${vendorName}`)),

  // Dynamic import with retry
  withRetry: async <T>(
    importFunc: () => Promise<T>, 
    retries: number = 3
  ): Promise<T> => {
    for (let i = 0; i < retries; i++) {
      try {
        return await importFunc();
      } catch (error) {
        if (i === retries - 1) throw error;
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
    throw new Error('Max retries exceeded');
  },
};

// Performance monitoring
export const usePerformanceMonitor = () => {
  useEffect(() => {
    // Monitor Core Web Vitals
    if ('web-vital' in window) {
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS(console.log);
        getFID(console.log);
        getFCP(console.log);
        getLCP(console.log);
        getTTFB(console.log);
      });
    }

    // Monitor bundle sizes
    if (process.env.NODE_ENV === 'development') {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'navigation') {
            console.log('Navigation timing:', entry);
          }
        });
      });
      observer.observe({ entryTypes: ['navigation'] });
    }
  }, []);
};
